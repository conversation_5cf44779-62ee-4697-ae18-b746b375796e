import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import Login from './pages/Login'
import EmployerDashboard from './pages/EmployerDashboard'
import ManagerDashboard from './pages/ManagerDashboard'
import LeadsPage from './pages/LeadsPage'
import ManagersPage from './pages/ManagersPage'
import Layout from './components/Layout'
import './App.css'

// Protected Route Component
const ProtectedRoute = ({ children, allowedRoles = [] }) => {
  const { user, loading } = useAuth()

  if (loading) {
    return <div className="loading">Loading...</div>
  }

  if (!user) {
    return <Navigate to="/login" replace />
  }

  if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
    return <Navigate to="/unauthorized" replace />
  }

  return children
}

function App() {
  return (
    <AuthProvider>
      <Router>
        <div className="app">
          <Routes>
            {/* Public Routes */}
            <Route path="/login" element={<Login />} />

            {/* Protected Routes */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }>
              {/* Employer Routes */}
              <Route index element={
                <ProtectedRoute allowedRoles={['employer']}>
                  <EmployerDashboard />
                </ProtectedRoute>
              } />
              <Route path="leads" element={
                <ProtectedRoute allowedRoles={['employer']}>
                  <LeadsPage />
                </ProtectedRoute>
              } />
              <Route path="managers" element={
                <ProtectedRoute allowedRoles={['employer']}>
                  <ManagersPage />
                </ProtectedRoute>
              } />

              {/* Manager Routes */}
              <Route path="manager-dashboard" element={
                <ProtectedRoute allowedRoles={['manager']}>
                  <ManagerDashboard />
                </ProtectedRoute>
              } />
            </Route>

            {/* Fallback Routes */}
            <Route path="/unauthorized" element={
              <div className="error-page">
                <h2>Unauthorized Access</h2>
                <p>You don't have permission to access this page.</p>
              </div>
            } />
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </AuthProvider>
  )
}

export default App
