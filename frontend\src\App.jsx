import { useState, useEffect } from 'react'
import axios from 'axios'
import ApiDemo from './components/ApiDemo'
import './App.css'

function App() {
  const [leads, setLeads] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [newLead, setNewLead] = useState({
    name: '',
    email: '',
    phone: '',
    company: ''
  })

  // Base URL for your backend API (adjust as needed)
  const API_BASE_URL = 'http://localhost:3000/api'

  // Fetch leads from backend
  const fetchLeads = async () => {
    setLoading(true)
    setError('')
    try {
      const response = await axios.get(`${API_BASE_URL}/leads`)
      setLeads(response.data)
    } catch (err) {
      setError('Failed to fetch leads: ' + (err.response?.data?.message || err.message))
      console.error('Error fetching leads:', err)
    } finally {
      setLoading(false)
    }
  }

  // Add new lead
  const addLead = async (e) => {
    e.preventDefault()
    if (!newLead.name || !newLead.email) {
      setError('Name and email are required')
      return
    }

    setLoading(true)
    setError('')
    try {
      const response = await axios.post(`${API_BASE_URL}/leads`, newLead)
      setLeads([...leads, response.data])
      setNewLead({ name: '', email: '', phone: '', company: '' })
    } catch (err) {
      setError('Failed to add lead: ' + (err.response?.data?.message || err.message))
      console.error('Error adding lead:', err)
    } finally {
      setLoading(false)
    }
  }

  // Delete lead
  const deleteLead = async (id) => {
    setLoading(true)
    setError('')
    try {
      await axios.delete(`${API_BASE_URL}/leads/${id}`)
      setLeads(leads.filter(lead => lead.id !== id))
    } catch (err) {
      setError('Failed to delete lead: ' + (err.response?.data?.message || err.message))
      console.error('Error deleting lead:', err)
    } finally {
      setLoading(false)
    }
  }

  // Test API connection
  const testConnection = async () => {
    setLoading(true)
    setError('')
    try {
      const response = await axios.get(`${API_BASE_URL}/health`)
      alert('API connection successful: ' + JSON.stringify(response.data))
    } catch (err) {
      setError('API connection failed: ' + (err.response?.data?.message || err.message))
      console.error('Error testing connection:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    // Uncomment to fetch leads on component mount
    // fetchLeads()
  }, [])

  return (
    <div className="app">
      <header className="app-header">
        <h1>Lead Management CRM</h1>
        <p>Simple React + Vite frontend with Axios HTTP requests</p>
      </header>

      <main className="app-main">
        {error && (
          <div className="error-message">
            <strong>Error:</strong> {error}
          </div>
        )}

        <section className="api-controls">
          <h2>API Controls</h2>
          <div className="button-group">
            <button onClick={testConnection} disabled={loading}>
              Test API Connection
            </button>
            <button onClick={fetchLeads} disabled={loading}>
              Fetch Leads
            </button>
          </div>
        </section>

        <section className="add-lead">
          <h2>Add New Lead</h2>
          <form onSubmit={addLead} className="lead-form">
            <div className="form-group">
              <input
                type="text"
                placeholder="Name *"
                value={newLead.name}
                onChange={(e) => setNewLead({...newLead, name: e.target.value})}
                required
              />
              <input
                type="email"
                placeholder="Email *"
                value={newLead.email}
                onChange={(e) => setNewLead({...newLead, email: e.target.value})}
                required
              />
            </div>
            <div className="form-group">
              <input
                type="tel"
                placeholder="Phone"
                value={newLead.phone}
                onChange={(e) => setNewLead({...newLead, phone: e.target.value})}
              />
              <input
                type="text"
                placeholder="Company"
                value={newLead.company}
                onChange={(e) => setNewLead({...newLead, company: e.target.value})}
              />
            </div>
            <button type="submit" disabled={loading}>
              {loading ? 'Adding...' : 'Add Lead'}
            </button>
          </form>
        </section>

        <section className="leads-list">
          <h2>Leads ({leads.length})</h2>
          {loading && <p>Loading...</p>}
          {leads.length === 0 && !loading ? (
            <p>No leads found. Click "Fetch Leads" to load from API or add a new lead.</p>
          ) : (
            <div className="leads-grid">
              {leads.map((lead) => (
                <div key={lead.id} className="lead-card">
                  <h3>{lead.name}</h3>
                  <p><strong>Email:</strong> {lead.email}</p>
                  {lead.phone && <p><strong>Phone:</strong> {lead.phone}</p>}
                  {lead.company && <p><strong>Company:</strong> {lead.company}</p>}
                  <button
                    onClick={() => deleteLead(lead.id)}
                    className="delete-btn"
                    disabled={loading}
                  >
                    Delete
                  </button>
                </div>
              ))}
            </div>
          )}
        </section>

        <ApiDemo />
      </main>
    </div>
  )
}

export default App
