# Lead Management CRM - Frontend

A simple React frontend built with Vite and JavaScript, featuring Axios for HTTP requests.

## Features

- **React + Vite**: Fast development with hot module replacement
- **Axios Integration**: HTTP client for API requests with interceptors
- **Lead Management UI**: Simple CRUD interface for managing leads
- **API Demo**: Interactive component to test different HTTP methods
- **Responsive Design**: Mobile-friendly CSS layout
- **Error Handling**: Comprehensive error handling for API requests

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn

### Installation

1. Navigate to the frontend directory:
   ```bash
   cd frontend
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   npm run dev
   ```

4. Open your browser and visit `http://localhost:5173`

## Project Structure

```
frontend/
├── src/
│   ├── components/
│   │   └── ApiDemo.jsx          # Interactive API testing component
│   ├── services/
│   │   └── api.js               # Axios configuration and API service
│   ├── App.jsx                  # Main application component
│   ├── App.css                  # Application styles
│   ├── main.jsx                 # Application entry point
│   └── index.css                # Global styles
├── public/
├── package.json
└── vite.config.js
```

## API Integration

The frontend is configured to connect to a backend API running on `http://localhost:3000/api`.

### Available API Endpoints

The UI expects the following endpoints:

- `GET /api/health` - Health check
- `POST /api/leads` - Create a new lead
- `GET /api/leads` - Get all leads
- `GET /api/leads/:id` - Get a specific lead
- `PUT /api/leads/:id` - Update a lead
- `DELETE /api/leads/:id` - Delete a lead

### Axios Configuration

The app includes a configured Axios instance with:

- Base URL configuration
- Request/response interceptors
- Error handling
- Request logging

See `src/services/api.js` for the complete configuration.

## Components

### Main App Component

The main `App.jsx` component includes:

- Lead management interface
- Form for adding new leads
- Lead listing with delete functionality
- API connection testing
- Error handling and loading states

### API Demo Component

The `ApiDemo.jsx` component provides:

- Interactive buttons for testing different HTTP methods
- Request/response display
- Error handling demonstration
- Example request payloads

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

### Customization

To customize the API base URL, update the `API_BASE_URL` constant in `App.jsx` or modify the `baseURL` in `src/services/api.js`.

## Backend Integration

This frontend is designed to work with the Lead Management CRM backend. Make sure your backend server is running on `http://localhost:3000` before testing the API functionality.
