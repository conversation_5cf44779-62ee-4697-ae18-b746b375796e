# Lead Management CRM - Frontend

A modern, multi-page React frontend built with Vite and JavaScript, featuring role-based authentication and comprehensive lead management capabilities.

## 🚀 Features

### Authentication & Authorization
- **JWT-based Authentication**: Secure login system with token management
- **Role-based Access Control**: Different interfaces for Employers and Managers
- **Protected Routes**: Automatic redirection based on authentication status
- **Persistent Sessions**: Token storage with automatic logout on expiration

### Multi-Page Architecture
- **React Router**: Client-side routing with multiple pages
- **Responsive Navigation**: Dynamic navigation based on user role
- **Layout System**: Consistent header and navigation across pages

### Employer Features
- **Dashboard**: Overview with lead statistics and quick actions
- **Lead Management**: Full CRUD operations for leads
- **Manager Management**: Add, edit, and remove managers
- **Lead Assignment**: Assign leads to specific managers
- **Filtering & Search**: Filter leads by status and manager

### Manager Features
- **Personal Dashboard**: View assigned leads with status overview
- **Lead Updates**: Update lead status and add notes
- **Status Tracking**: Visual indicators for lead progress
- **Notes System**: Add contextual notes to leads

### UI/UX
- **Modern Design**: Clean, professional interface with gradients
- **Responsive Layout**: Mobile-friendly design that works on all devices
- **Interactive Components**: Hover effects, animations, and transitions
- **Loading States**: Proper loading indicators and error handling
- **Modal System**: Clean modal dialogs for forms and actions

## 🛠️ Technology Stack

- **React 18**: Modern React with hooks and functional components
- **Vite**: Fast build tool with hot module replacement
- **React Router v6**: Client-side routing and navigation
- **Axios**: HTTP client with interceptors and error handling
- **CSS3**: Modern CSS with flexbox, grid, and custom properties
- **JavaScript ES6+**: Modern JavaScript features

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or higher)
- npm or yarn
- Backend server running on `http://localhost:3000`

### Installation

1. **Navigate to the frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   ```

3. **Start the development server:**
   ```bash
   npm run dev
   ```

4. **Open your browser and visit:**
   ```
   http://localhost:5173
   ```

## 🔐 Authentication

The application uses JWT tokens for authentication. Demo accounts:

### Employer Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Access**: Full system access including lead and manager management

### Manager Account
- **Email**: `<EMAIL>`
- **Password**: `password123`
- **Access**: View and update assigned leads only

## 🌐 API Integration

### Backend Requirements
The frontend expects a backend API running on `http://localhost:3000` with the following endpoints:

#### Authentication
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user profile

#### Employer Endpoints
- `GET /api/employer/dashboard-stats` - Get lead statistics
- `GET /api/employer/managers` - Get all managers
- `POST /api/employer/managers` - Create new manager
- `PUT /api/employer/managers/:id` - Update manager
- `DELETE /api/employer/managers/:id` - Delete manager
- `GET /api/employer/leads` - Get all leads (with filters)
- `POST /api/employer/leads` - Create new lead
- `PUT /api/employer/leads/:id` - Update lead
- `DELETE /api/employer/leads/:id` - Delete lead

#### Manager Endpoints
- `GET /api/managers/leads` - Get assigned leads
- `PATCH /api/managers/leads/:id` - Update lead status and add notes

## 🔧 Development

### Available Scripts
- `npm run dev` - Start development server with hot reload
- `npm run build` - Build for production
- `npm run preview` - Preview production build locally
- `npm run lint` - Run ESLint for code quality

## 🤝 Integration with Backend

This frontend is specifically designed to work with the existing Lead Management CRM backend. Make sure your backend server is running before starting the frontend application.
