import { useState, useEffect } from 'react'
import { employerAPI } from '../services/api'
import LeadModal from '../components/LeadModal'
import './LeadsPage.css'

const LeadsPage = () => {
  const [leads, setLeads] = useState([])
  const [managers, setManagers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [editingLead, setEditingLead] = useState(null)
  const [filters, setFilters] = useState({
    status: '',
    managerId: ''
  })

  useEffect(() => {
    fetchData()
  }, [])

  useEffect(() => {
    fetchLeads()
  }, [filters])

  const fetchData = async () => {
    try {
      const managersRes = await employerAPI.getManagers()
      setManagers(managersRes.data)
      await fetchLeads()
    } catch (err) {
      setError('Failed to load data')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const fetchLeads = async () => {
    try {
      const params = {}
      if (filters.status) params.status = filters.status
      if (filters.managerId) params.managerId = filters.managerId
      
      const response = await employerAPI.getLeads(params)
      setLeads(response.data)
    } catch (err) {
      setError('Failed to load leads')
      console.error('Error:', err)
    }
  }

  const handleCreateLead = () => {
    setEditingLead(null)
    setShowModal(true)
  }

  const handleEditLead = (lead) => {
    setEditingLead(lead)
    setShowModal(true)
  }

  const handleDeleteLead = async (leadId) => {
    if (!window.confirm('Are you sure you want to delete this lead?')) return

    try {
      await employerAPI.deleteLead(leadId)
      setLeads(leads.filter(lead => lead._id !== leadId))
    } catch (err) {
      setError('Failed to delete lead')
      console.error('Error:', err)
    }
  }

  const handleModalClose = () => {
    setShowModal(false)
    setEditingLead(null)
  }

  const handleModalSuccess = () => {
    setShowModal(false)
    setEditingLead(null)
    fetchLeads()
  }

  const getStatusColor = (status) => {
    const colors = {
      PENDING: '#ffc107',
      IN_PROGRESS: '#007bff',
      COMPLETED: '#28a745',
      CANCELED: '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  const getManagerName = (managerId) => {
    const manager = managers.find(m => m._id === managerId)
    return manager ? manager.name : 'Unassigned'
  }

  if (loading) {
    return <div className="loading">Loading leads...</div>
  }

  return (
    <div className="leads-page">
      <div className="page-header">
        <div>
          <h1>👥 Leads Management</h1>
          <p>Manage and track all your leads</p>
        </div>
        <button onClick={handleCreateLead} className="create-btn">
          + Create Lead
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Filters */}
      <div className="filters">
        <div className="filter-group">
          <label>Status:</label>
          <select 
            value={filters.status} 
            onChange={(e) => setFilters({...filters, status: e.target.value})}
          >
            <option value="">All Statuses</option>
            <option value="PENDING">Pending</option>
            <option value="IN_PROGRESS">In Progress</option>
            <option value="COMPLETED">Completed</option>
            <option value="CANCELED">Canceled</option>
          </select>
        </div>

        <div className="filter-group">
          <label>Manager:</label>
          <select 
            value={filters.managerId} 
            onChange={(e) => setFilters({...filters, managerId: e.target.value})}
          >
            <option value="">All Managers</option>
            {managers.map(manager => (
              <option key={manager._id} value={manager._id}>
                {manager.name}
              </option>
            ))}
          </select>
        </div>

        <button 
          onClick={() => setFilters({ status: '', managerId: '' })}
          className="clear-filters-btn"
        >
          Clear Filters
        </button>
      </div>

      {/* Leads Table */}
      <div className="leads-table-container">
        {leads.length > 0 ? (
          <table className="leads-table">
            <thead>
              <tr>
                <th>Contact</th>
                <th>Company</th>
                <th>Status</th>
                <th>Manager</th>
                <th>Created</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {leads.map(lead => (
                <tr key={lead._id}>
                  <td>
                    <div className="contact-info">
                      <div className="contact-name">{lead.contactName}</div>
                      <div className="contact-email">{lead.contactEmail}</div>
                    </div>
                  </td>
                  <td>{lead.companyName}</td>
                  <td>
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(lead.status) }}
                    >
                      {lead.status.replace('_', ' ')}
                    </span>
                  </td>
                  <td>{getManagerName(lead.manager)}</td>
                  <td>{new Date(lead.createdAt).toLocaleDateString()}</td>
                  <td>
                    <div className="action-buttons">
                      <button 
                        onClick={() => handleEditLead(lead)}
                        className="edit-btn"
                      >
                        Edit
                      </button>
                      <button 
                        onClick={() => handleDeleteLead(lead._id)}
                        className="delete-btn"
                      >
                        Delete
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="empty-state">
            <h3>No leads found</h3>
            <p>Create your first lead to get started</p>
            <button onClick={handleCreateLead} className="create-btn">
              + Create Lead
            </button>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <LeadModal
          lead={editingLead}
          managers={managers}
          onClose={handleModalClose}
          onSuccess={handleModalSuccess}
        />
      )}
    </div>
  )
}

export default LeadsPage
