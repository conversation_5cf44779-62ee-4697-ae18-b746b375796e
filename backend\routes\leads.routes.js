import express from 'express';
import {
  getAllLeads,
  getLeadById,
  createLead,
  updateLead,
  deleteLead,
  healthCheck
} from '../controllers/leads.controller.js';

const router = express.Router();

// Health check endpoint
router.get('/health', healthCheck);

// Lead CRUD routes
router.get('/leads', getAllLeads);
router.get('/leads/:id', getLeadById);
router.post('/leads', createLead);
router.put('/leads/:id', updateLead);
router.delete('/leads/:id', deleteLead);

export { router as LeadsRouter };
