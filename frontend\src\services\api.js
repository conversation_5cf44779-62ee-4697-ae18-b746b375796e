import axios from 'axios'

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:3000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor for adding auth tokens or logging
api.interceptors.request.use(
  (config) => {
    console.log(`Making ${config.method?.toUpperCase()} request to ${config.url}`)
    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor for handling common errors
api.interceptors.response.use(
  (response) => {
    console.log(`Response received:`, response.status, response.statusText)
    return response
  },
  (error) => {
    console.error('Response error:', error.response?.status, error.response?.statusText)
    
    // Handle common HTTP errors
    if (error.response?.status === 401) {
      console.error('Unauthorized access - redirect to login')
    } else if (error.response?.status === 403) {
      console.error('Forbidden access')
    } else if (error.response?.status === 500) {
      console.error('Server error')
    }
    
    return Promise.reject(error)
  }
)

// API service methods
export const apiService = {
  // Health check
  healthCheck: () => api.get('/health'),
  
  // Lead management
  leads: {
    getAll: () => api.get('/leads'),
    getById: (id) => api.get(`/leads/${id}`),
    create: (leadData) => api.post('/leads', leadData),
    update: (id, leadData) => api.put(`/leads/${id}`, leadData),
    delete: (id) => api.delete(`/leads/${id}`)
  },
  
  // Example of other endpoints you might have
  users: {
    getAll: () => api.get('/users'),
    getById: (id) => api.get(`/users/${id}`),
    create: (userData) => api.post('/users', userData),
    update: (id, userData) => api.put(`/users/${id}`, userData),
    delete: (id) => api.delete(`/users/${id}`)
  }
}

// Export the configured axios instance for direct use if needed
export default api
