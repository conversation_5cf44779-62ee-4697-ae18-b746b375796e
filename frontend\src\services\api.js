import axios from 'axios'

const API_BASE_URL = 'http://localhost:3000/api'

// Create axios instance
const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authAPI = {
  login: (email, password) => api.post('/auth/login', { email, password }),
  getProfile: () => api.get('/auth/me')
}

// Employer API
export const employerAPI = {
  getDashboardStats: () => api.get('/employer/dashboard-stats'),
  
  // Managers
  getManagers: () => api.get('/employer/managers'),
  createManager: (data) => api.post('/employer/managers', data),
  updateManager: (id, data) => api.put(`/employer/managers/${id}`, data),
  deleteManager: (id) => api.delete(`/employer/managers/${id}`),
  
  // Leads
  getLeads: (params = {}) => api.get('/employer/leads', { params }),
  createLead: (data) => api.post('/employer/leads', data),
  updateLead: (id, data) => api.put(`/employer/leads/${id}`, data),
  deleteLead: (id) => api.delete(`/employer/leads/${id}`)
}

// Manager API
export const managerAPI = {
  getAssignedLeads: () => api.get('/managers/leads'),
  updateLead: (id, data) => api.patch(`/managers/leads/${id}`, data)
}

export default api
