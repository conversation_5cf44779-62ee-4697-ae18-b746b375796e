{"name": "backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "node --experimental-vm-modules node_modules/jest/bin/jest.js", "dev": "nodemon server.js"}, "jest": {"testEnvironment": "node", "transform": {}, "setupFilesAfterEnv": ["./tests/setup.js"]}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^6.0.0", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.0", "nodemon": "^3.1.10"}, "devDependencies": {"jest": "^29.7.0", "supertest": "^6.3.3"}}