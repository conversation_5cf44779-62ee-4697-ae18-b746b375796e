import { useState, useEffect } from 'react'
import { employerAPI } from '../services/api'
import './Modal.css'

const ManagerModal = ({ manager, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (manager) {
      setFormData({
        name: manager.name || '',
        email: manager.email || '',
        password: '' // Don't pre-fill password for security
      })
    }
  }, [manager])

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const submitData = { ...formData }
      
      // For updates, only include password if it's provided
      if (manager && !submitData.password) {
        delete submitData.password
      }

      if (manager) {
        await employerAPI.updateManager(manager._id, submitData)
      } else {
        await employerAPI.createManager(submitData)
      }
      onSuccess()
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to save manager')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>{manager ? 'Edit Manager' : 'Add New Manager'}</h2>
          <button onClick={onClose} className="close-btn">×</button>
        </div>

        <form onSubmit={handleSubmit} className="modal-form">
          {error && (
            <div className="error-message">
              {error}
            </div>
          )}

          <div className="form-group">
            <label htmlFor="name">Full Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
              disabled={loading}
              placeholder="Enter manager's full name"
            />
          </div>

          <div className="form-group">
            <label htmlFor="email">Email Address *</label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              required
              disabled={loading}
              placeholder="Enter email address"
            />
          </div>

          <div className="form-group">
            <label htmlFor="password">
              Password {manager ? '(leave blank to keep current)' : '*'}
            </label>
            <input
              type="password"
              id="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              required={!manager}
              disabled={loading}
              placeholder={manager ? "Enter new password" : "Enter password"}
            />
          </div>

          <div className="modal-actions">
            <button type="button" onClick={onClose} className="cancel-btn">
              Cancel
            </button>
            <button type="submit" disabled={loading} className="submit-btn">
              {loading ? 'Saving...' : (manager ? 'Update Manager' : 'Add Manager')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ManagerModal
