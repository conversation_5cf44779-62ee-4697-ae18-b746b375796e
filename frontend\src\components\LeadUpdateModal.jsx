import { useState } from 'react'
import { managerAPI } from '../services/api'
import './Modal.css'

const LeadUpdateModal = ({ lead, onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    status: lead.status,
    note: ''
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
    setError('')
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const updateData = {}
      
      // Only include status if it changed
      if (formData.status !== lead.status) {
        updateData.status = formData.status
      }
      
      // Only include note if provided
      if (formData.note.trim()) {
        updateData.note = formData.note.trim()
      }

      // Check if there's anything to update
      if (Object.keys(updateData).length === 0) {
        setError('Please make changes before updating')
        setLoading(false)
        return
      }

      await managerAPI.updateLead(lead._id, updateData)
      onSuccess()
    } catch (err) {
      setError(err.response?.data?.error || 'Failed to update lead')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    const colors = {
      PENDING: '#ffc107',
      IN_PROGRESS: '#007bff',
      COMPLETED: '#28a745',
      CANCELED: '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content" onClick={(e) => e.stopPropagation()}>
        <div className="modal-header">
          <h2>Update Lead Status</h2>
          <button onClick={onClose} className="close-btn">×</button>
        </div>

        <div className="modal-form">
          {/* Lead Info */}
          <div className="lead-summary">
            <h3>{lead.contactName}</h3>
            <p>{lead.contactEmail}</p>
            <p><strong>Company:</strong> {lead.companyName}</p>
            <div className="current-status">
              <strong>Current Status:</strong>
              <span 
                className="status-badge"
                style={{ backgroundColor: getStatusColor(lead.status), marginLeft: '0.5rem' }}
              >
                {lead.status.replace('_', ' ')}
              </span>
            </div>
          </div>

          {/* Existing Notes */}
          {lead.notes && lead.notes.length > 0 && (
            <div className="existing-notes">
              <h4>Previous Notes:</h4>
              <div className="notes-list">
                {lead.notes.slice(-3).map((note, index) => (
                  <div key={index} className="note-item">
                    {note}
                  </div>
                ))}
                {lead.notes.length > 3 && (
                  <div className="note-item more">
                    ... and {lead.notes.length - 3} more notes
                  </div>
                )}
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            {error && (
              <div className="error-message">
                {error}
              </div>
            )}

            <div className="form-group">
              <label htmlFor="status">Update Status</label>
              <select
                id="status"
                name="status"
                value={formData.status}
                onChange={handleChange}
                disabled={loading}
              >
                <option value="PENDING">Pending</option>
                <option value="IN_PROGRESS">In Progress</option>
                <option value="COMPLETED">Completed</option>
                <option value="CANCELED">Canceled</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="note">Add Note (Optional)</label>
              <textarea
                id="note"
                name="note"
                value={formData.note}
                onChange={handleChange}
                disabled={loading}
                placeholder="Add a note about this update..."
                rows="4"
              />
            </div>

            <div className="modal-actions">
              <button type="button" onClick={onClose} className="cancel-btn">
                Cancel
              </button>
              <button type="submit" disabled={loading} className="submit-btn">
                {loading ? 'Updating...' : 'Update Lead'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default LeadUpdateModal
