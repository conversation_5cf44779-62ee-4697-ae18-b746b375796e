import { useState } from 'react'
import axios from 'axios'

const ApiDemo = () => {
  const [response, setResponse] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const baseURL = 'http://localhost:3000/api'

  const makeRequest = async (method, endpoint, data = null) => {
    setLoading(true)
    setError('')
    setResponse('')

    try {
      let result
      const config = {
        url: `${baseURL}${endpoint}`,
        method: method.toLowerCase(),
        ...(data && { data })
      }

      result = await axios(config)
      setResponse(JSON.stringify(result.data, null, 2))
    } catch (err) {
      setError(`${err.response?.status || 'Network'} Error: ${err.response?.data?.message || err.message}`)
      console.error('API Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const demoRequests = [
    {
      name: 'GET Health Check',
      method: 'GET',
      endpoint: '/health',
      description: 'Check if the API is running'
    },
    {
      name: 'GET All Leads',
      method: 'GET',
      endpoint: '/leads',
      description: 'Fetch all leads from the database'
    },
    {
      name: 'POST Create Lead',
      method: 'POST',
      endpoint: '/leads',
      data: {
        name: 'Demo Lead',
        email: '<EMAIL>',
        phone: '************',
        company: 'Demo Company'
      },
      description: 'Create a new lead'
    },
    {
      name: 'GET Lead by ID',
      method: 'GET',
      endpoint: '/leads/1',
      description: 'Fetch a specific lead by ID'
    },
    {
      name: 'PUT Update Lead',
      method: 'PUT',
      endpoint: '/leads/1',
      data: {
        name: 'Updated Lead',
        email: '<EMAIL>',
        phone: '************',
        company: 'Updated Company'
      },
      description: 'Update an existing lead'
    },
    {
      name: 'DELETE Lead',
      method: 'DELETE',
      endpoint: '/leads/1',
      description: 'Delete a lead by ID'
    }
  ]

  return (
    <div className="api-demo">
      <h2>API Demo - HTTP Request Examples</h2>
      <p>Click the buttons below to test different HTTP methods with Axios:</p>
      
      <div className="demo-buttons">
        {demoRequests.map((request, index) => (
          <div key={index} className="demo-request">
            <button
              onClick={() => makeRequest(request.method, request.endpoint, request.data)}
              disabled={loading}
              className={`demo-btn ${request.method.toLowerCase()}`}
            >
              {request.name}
            </button>
            <p className="demo-description">{request.description}</p>
            {request.data && (
              <details className="demo-data">
                <summary>Request Data</summary>
                <pre>{JSON.stringify(request.data, null, 2)}</pre>
              </details>
            )}
          </div>
        ))}
      </div>

      <div className="demo-output">
        <h3>Response</h3>
        {loading && <p className="loading">Making request...</p>}
        {error && (
          <div className="error-output">
            <strong>Error:</strong>
            <pre>{error}</pre>
          </div>
        )}
        {response && (
          <div className="success-output">
            <strong>Success:</strong>
            <pre>{response}</pre>
          </div>
        )}
      </div>

      <style jsx>{`
        .api-demo {
          background: #f8f9fa;
          padding: 2rem;
          border-radius: 8px;
          margin: 2rem 0;
        }

        .demo-buttons {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: 1rem;
          margin: 2rem 0;
        }

        .demo-request {
          background: white;
          padding: 1rem;
          border-radius: 4px;
          border: 1px solid #e9ecef;
        }

        .demo-btn {
          width: 100%;
          padding: 0.75rem;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          font-weight: bold;
          margin-bottom: 0.5rem;
        }

        .demo-btn.get {
          background: #28a745;
          color: white;
        }

        .demo-btn.post {
          background: #007bff;
          color: white;
        }

        .demo-btn.put {
          background: #ffc107;
          color: black;
        }

        .demo-btn.delete {
          background: #dc3545;
          color: white;
        }

        .demo-btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
        }

        .demo-description {
          font-size: 0.9rem;
          color: #666;
          margin: 0.5rem 0;
        }

        .demo-data {
          margin-top: 0.5rem;
        }

        .demo-data pre {
          background: #f8f9fa;
          padding: 0.5rem;
          border-radius: 4px;
          font-size: 0.8rem;
          overflow-x: auto;
        }

        .demo-output {
          background: white;
          padding: 1.5rem;
          border-radius: 4px;
          border: 1px solid #e9ecef;
        }

        .loading {
          color: #007bff;
          font-style: italic;
        }

        .error-output {
          color: #dc3545;
        }

        .success-output {
          color: #28a745;
        }

        .error-output pre,
        .success-output pre {
          background: #f8f9fa;
          padding: 1rem;
          border-radius: 4px;
          overflow-x: auto;
          white-space: pre-wrap;
          margin-top: 0.5rem;
        }
      `}</style>
    </div>
  )
}

export default ApiDemo
