import { Lead } from '../models/leads.model.js';

// Get all leads
export const getAllLeads = async (req, res) => {
  try {
    const leads = await Lead.find().populate('manager', 'name email');
    
    // Transform the data to match frontend expectations
    const transformedLeads = leads.map(lead => ({
      id: lead._id,
      name: lead.contactName,
      email: lead.contactEmail,
      company: lead.companyName,
      phone: lead.contactPhone || '',
      status: lead.status,
      manager: lead.manager,
      notes: lead.notes,
      createdAt: lead.createdAt,
      updatedAt: lead.updatedAt
    }));

    res.status(200).json(transformedLeads);
  } catch (error) {
    console.error('Error fetching leads:', error);
    res.status(500).json({ 
      message: 'Failed to fetch leads', 
      error: error.message 
    });
  }
};

// Get lead by ID
export const getLeadById = async (req, res) => {
  try {
    const { id } = req.params;
    const lead = await Lead.findById(id).populate('manager', 'name email');
    
    if (!lead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    // Transform the data to match frontend expectations
    const transformedLead = {
      id: lead._id,
      name: lead.contactName,
      email: lead.contactEmail,
      company: lead.companyName,
      phone: lead.contactPhone || '',
      status: lead.status,
      manager: lead.manager,
      notes: lead.notes,
      createdAt: lead.createdAt,
      updatedAt: lead.updatedAt
    };

    res.status(200).json(transformedLead);
  } catch (error) {
    console.error('Error fetching lead:', error);
    res.status(500).json({ 
      message: 'Failed to fetch lead', 
      error: error.message 
    });
  }
};

// Create new lead
export const createLead = async (req, res) => {
  try {
    const { name, email, company, phone } = req.body;

    // Validate required fields
    if (!name || !email) {
      return res.status(400).json({ 
        message: 'Name and email are required' 
      });
    }

    // Check if lead with this email already exists
    const existingLead = await Lead.findOne({ contactEmail: email });
    if (existingLead) {
      return res.status(409).json({ 
        message: 'Lead with this email already exists' 
      });
    }

    // Create new lead with transformed field names
    const newLead = new Lead({
      contactName: name,
      contactEmail: email,
      companyName: company || 'Unknown Company',
      contactPhone: phone || '',
      status: 'PENDING'
    });

    const savedLead = await newLead.save();

    // Transform response to match frontend expectations
    const transformedLead = {
      id: savedLead._id,
      name: savedLead.contactName,
      email: savedLead.contactEmail,
      company: savedLead.companyName,
      phone: savedLead.contactPhone || '',
      status: savedLead.status,
      notes: savedLead.notes,
      createdAt: savedLead.createdAt,
      updatedAt: savedLead.updatedAt
    };

    res.status(201).json(transformedLead);
  } catch (error) {
    console.error('Error creating lead:', error);
    res.status(500).json({ 
      message: 'Failed to create lead', 
      error: error.message 
    });
  }
};

// Update lead
export const updateLead = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, email, company, phone, status } = req.body;

    const updateData = {};
    if (name) updateData.contactName = name;
    if (email) updateData.contactEmail = email;
    if (company) updateData.companyName = company;
    if (phone !== undefined) updateData.contactPhone = phone;
    if (status) updateData.status = status;

    const updatedLead = await Lead.findByIdAndUpdate(
      id, 
      updateData, 
      { new: true, runValidators: true }
    ).populate('manager', 'name email');

    if (!updatedLead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    // Transform response to match frontend expectations
    const transformedLead = {
      id: updatedLead._id,
      name: updatedLead.contactName,
      email: updatedLead.contactEmail,
      company: updatedLead.companyName,
      phone: updatedLead.contactPhone || '',
      status: updatedLead.status,
      manager: updatedLead.manager,
      notes: updatedLead.notes,
      createdAt: updatedLead.createdAt,
      updatedAt: updatedLead.updatedAt
    };

    res.status(200).json(transformedLead);
  } catch (error) {
    console.error('Error updating lead:', error);
    res.status(500).json({ 
      message: 'Failed to update lead', 
      error: error.message 
    });
  }
};

// Delete lead
export const deleteLead = async (req, res) => {
  try {
    const { id } = req.params;
    
    const deletedLead = await Lead.findByIdAndDelete(id);
    
    if (!deletedLead) {
      return res.status(404).json({ message: 'Lead not found' });
    }

    res.status(200).json({ 
      message: 'Lead deleted successfully',
      id: deletedLead._id
    });
  } catch (error) {
    console.error('Error deleting lead:', error);
    res.status(500).json({ 
      message: 'Failed to delete lead', 
      error: error.message 
    });
  }
};

// Health check endpoint
export const healthCheck = async (req, res) => {
  try {
    // Check database connection
    const dbStatus = await Lead.countDocuments();
    
    res.status(200).json({
      status: 'OK',
      message: 'Lead Management API is running',
      timestamp: new Date().toISOString(),
      database: 'Connected',
      totalLeads: dbStatus
    });
  } catch (error) {
    res.status(500).json({
      status: 'ERROR',
      message: 'Database connection failed',
      error: error.message
    });
  }
};
