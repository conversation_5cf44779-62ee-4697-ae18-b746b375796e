import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3001;

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// Simple test routes
app.get('/api/test', (req, res) => {
  console.log('Test route hit!');
  res.json({ message: 'Test API is working!' });
});

app.get('/api/health', (req, res) => {
  console.log('Health route hit!');
  res.json({ 
    status: 'OK', 
    message: 'Lead Management API is running',
    timestamp: new Date().toISOString()
  });
});

// Simple leads endpoints
app.get('/api/leads', (req, res) => {
  console.log('Get leads route hit!');
  res.json([
    { id: 1, name: 'Test Lead', email: '<EMAIL>', company: 'Test Company' }
  ]);
});

app.post('/api/leads', (req, res) => {
  console.log('Create lead route hit!', req.body);
  const { name, email, company, phone } = req.body;
  res.status(201).json({
    id: Date.now(),
    name,
    email,
    company,
    phone: phone || ''
  });
});

app.listen(PORT, () => {
  console.log(`Test server listening on port ${PORT}`);
  console.log(`Test URL: http://localhost:${PORT}/api/test`);
});
