import { useState, useEffect } from 'react'
import { managerAPI } from '../services/api'
import LeadUpdateModal from '../components/LeadUpdateModal'
import './Dashboard.css'
import './ManagerDashboard.css'

const ManagerDashboard = () => {
  const [leads, setLeads] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [selectedLead, setSelectedLead] = useState(null)
  const [filter, setFilter] = useState('ALL')

  useEffect(() => {
    fetchLeads()
  }, [])

  const fetchLeads = async () => {
    try {
      setLoading(true)
      const response = await managerAPI.getAssignedLeads()
      setLeads(response.data)
    } catch (err) {
      setError('Failed to load assigned leads')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleUpdateLead = (lead) => {
    setSelectedLead(lead)
    setShowModal(true)
  }

  const handleModalClose = () => {
    setShowModal(false)
    setSelectedLead(null)
  }

  const handleModalSuccess = () => {
    setShowModal(false)
    setSelectedLead(null)
    fetchLeads()
  }

  const getStatusColor = (status) => {
    const colors = {
      PENDING: '#ffc107',
      IN_PROGRESS: '#007bff',
      COMPLETED: '#28a745',
      CANCELED: '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  const getStatusIcon = (status) => {
    const icons = {
      PENDING: '⏳',
      IN_PROGRESS: '🔄',
      COMPLETED: '✅',
      CANCELED: '❌'
    }
    return icons[status] || '📋'
  }

  const filteredLeads = leads.filter(lead => {
    if (filter === 'ALL') return true
    return lead.status === filter
  })

  const getStats = () => {
    const stats = {
      PENDING: 0,
      IN_PROGRESS: 0,
      COMPLETED: 0,
      CANCELED: 0
    }
    
    leads.forEach(lead => {
      stats[lead.status] = (stats[lead.status] || 0) + 1
    })
    
    return stats
  }

  const stats = getStats()

  if (loading) {
    return <div className="loading">Loading your leads...</div>
  }

  return (
    <div className="dashboard manager-dashboard">
      <div className="dashboard-header">
        <h1>📋 My Assigned Leads</h1>
        <p>Manage and update the status of your assigned leads</p>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <h3>Total Assigned</h3>
            <div className="stat-number">{leads.length}</div>
          </div>
        </div>

        {Object.entries(stats).map(([status, count]) => (
          <div key={status} className="stat-card">
            <div className="stat-icon">{getStatusIcon(status)}</div>
            <div className="stat-content">
              <h3>{status.replace('_', ' ')}</h3>
              <div className="stat-number" style={{ color: getStatusColor(status) }}>
                {count}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Filter Tabs */}
      <div className="filter-tabs">
        {['ALL', 'PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELED'].map(status => (
          <button
            key={status}
            onClick={() => setFilter(status)}
            className={`filter-tab ${filter === status ? 'active' : ''}`}
          >
            {status === 'ALL' ? 'All Leads' : status.replace('_', ' ')}
            <span className="count">
              {status === 'ALL' ? leads.length : (stats[status] || 0)}
            </span>
          </button>
        ))}
      </div>

      {/* Leads List */}
      <div className="leads-section">
        {filteredLeads.length > 0 ? (
          <div className="leads-list">
            {filteredLeads.map(lead => (
              <div key={lead._id} className="lead-card">
                <div className="lead-header">
                  <div className="lead-info">
                    <h3>{lead.contactName}</h3>
                    <p className="lead-email">{lead.contactEmail}</p>
                    <p className="lead-company">{lead.companyName}</p>
                  </div>
                  <div className="lead-status">
                    <span 
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(lead.status) }}
                    >
                      {getStatusIcon(lead.status)} {lead.status.replace('_', ' ')}
                    </span>
                  </div>
                </div>

                <div className="lead-details">
                  <div className="lead-dates">
                    <span>Created: {new Date(lead.createdAt).toLocaleDateString()}</span>
                    {lead.updatedAt !== lead.createdAt && (
                      <span>Updated: {new Date(lead.updatedAt).toLocaleDateString()}</span>
                    )}
                  </div>
                  
                  {lead.notes && lead.notes.length > 0 && (
                    <div className="lead-notes">
                      <strong>Latest Note:</strong>
                      <p>{lead.notes[lead.notes.length - 1]}</p>
                    </div>
                  )}
                </div>

                <div className="lead-actions">
                  <button 
                    onClick={() => handleUpdateLead(lead)}
                    className="update-btn"
                  >
                    Update Status
                  </button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <h3>No leads found</h3>
            <p>
              {filter === 'ALL' 
                ? 'You have no assigned leads yet.' 
                : `No leads with status "${filter.replace('_', ' ')}" found.`
              }
            </p>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && selectedLead && (
        <LeadUpdateModal
          lead={selectedLead}
          onClose={handleModalClose}
          onSuccess={handleModalSuccess}
        />
      )}
    </div>
  )
}

export default ManagerDashboard
