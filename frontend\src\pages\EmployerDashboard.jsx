import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { employerAPI } from '../services/api'
import './Dashboard.css'

const EmployerDashboard = () => {
  const [stats, setStats] = useState({
    PENDING: 0,
    IN_PROGRESS: 0,
    COMPLETED: 0,
    CANCELED: 0
  })
  const [recentLeads, setRecentLeads] = useState([])
  const [managers, setManagers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)
      const [statsRes, leadsRes, managersRes] = await Promise.all([
        employerAPI.getDashboardStats(),
        employerAPI.getLeads(),
        employerAPI.getManagers()
      ])

      setStats(statsRes.data)
      setRecentLeads(leadsRes.data.slice(0, 5)) // Get recent 5 leads
      setManagers(managersRes.data)
    } catch (err) {
      setError('Failed to load dashboard data')
      console.error('Dashboard error:', err)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    const colors = {
      PENDING: '#ffc107',
      IN_PROGRESS: '#007bff',
      COMPLETED: '#28a745',
      CANCELED: '#dc3545'
    }
    return colors[status] || '#6c757d'
  }

  const getStatusIcon = (status) => {
    const icons = {
      PENDING: '⏳',
      IN_PROGRESS: '🔄',
      COMPLETED: '✅',
      CANCELED: '❌'
    }
    return icons[status] || '📋'
  }

  const totalLeads = Object.values(stats).reduce((sum, count) => sum + count, 0)

  if (loading) {
    return <div className="loading">Loading dashboard...</div>
  }

  return (
    <div className="dashboard">
      <div className="dashboard-header">
        <h1>📊 Employer Dashboard</h1>
        <p>Welcome back! Here's an overview of your lead management system.</p>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Stats Cards */}
      <div className="stats-grid">
        <div className="stat-card total">
          <div className="stat-icon">📈</div>
          <div className="stat-content">
            <h3>Total Leads</h3>
            <div className="stat-number">{totalLeads}</div>
          </div>
        </div>

        {Object.entries(stats).map(([status, count]) => (
          <div key={status} className="stat-card">
            <div className="stat-icon">{getStatusIcon(status)}</div>
            <div className="stat-content">
              <h3>{status.replace('_', ' ')}</h3>
              <div className="stat-number" style={{ color: getStatusColor(status) }}>
                {count}
              </div>
            </div>
          </div>
        ))}

        <div className="stat-card">
          <div className="stat-icon">👨‍💼</div>
          <div className="stat-content">
            <h3>Managers</h3>
            <div className="stat-number">{managers.length}</div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="quick-actions">
        <h2>Quick Actions</h2>
        <div className="action-buttons">
          <Link to="/leads" className="action-btn primary">
            <span>👥</span>
            Manage Leads
          </Link>
          <Link to="/managers" className="action-btn secondary">
            <span>👨‍💼</span>
            Manage Managers
          </Link>
        </div>
      </div>

      {/* Recent Leads */}
      <div className="recent-section">
        <div className="section-header">
          <h2>Recent Leads</h2>
          <Link to="/leads" className="view-all-link">View All →</Link>
        </div>
        
        {recentLeads.length > 0 ? (
          <div className="recent-leads">
            {recentLeads.map((lead) => (
              <div key={lead._id} className="lead-item">
                <div className="lead-info">
                  <h4>{lead.contactName}</h4>
                  <p>{lead.contactEmail}</p>
                  <span className="company">{lead.companyName}</span>
                </div>
                <div className="lead-status">
                  <span 
                    className="status-badge"
                    style={{ backgroundColor: getStatusColor(lead.status) }}
                  >
                    {lead.status.replace('_', ' ')}
                  </span>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="empty-state">
            <p>No leads found. <Link to="/leads">Create your first lead</Link></p>
          </div>
        )}
      </div>
    </div>
  )
}

export default EmployerDashboard
