import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../contexts/AuthContext'
import './Layout.css'

const Layout = () => {
  const { user, logout } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const handleLogout = () => {
    logout()
    navigate('/login')
  }

  const getNavItems = () => {
    if (user?.role === 'employer') {
      return [
        { path: '/', label: 'Dashboard', icon: '📊' },
        { path: '/leads', label: 'Leads', icon: '👥' },
        { path: '/managers', label: 'Managers', icon: '👨‍💼' }
      ]
    } else if (user?.role === 'manager') {
      return [
        { path: '/manager-dashboard', label: 'My Leads', icon: '📋' }
      ]
    }
    return []
  }

  const navItems = getNavItems()

  return (
    <div className="layout">
      <header className="header">
        <div className="header-content">
          <div className="logo">
            <h1>🏢 Lead CRM</h1>
          </div>
          
          <nav className="nav">
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`nav-link ${location.pathname === item.path ? 'active' : ''}`}
              >
                <span className="nav-icon">{item.icon}</span>
                {item.label}
              </Link>
            ))}
          </nav>

          <div className="user-menu">
            <div className="user-info">
              <span className="user-name">{user?.name}</span>
              <span className="user-role">{user?.role}</span>
            </div>
            <button onClick={handleLogout} className="logout-btn">
              Logout
            </button>
          </div>
        </div>
      </header>

      <main className="main-content">
        <Outlet />
      </main>
    </div>
  )
}

export default Layout
