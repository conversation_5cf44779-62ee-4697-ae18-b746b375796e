import { useState, useEffect } from 'react'
import { employerAPI } from '../services/api'
import ManagerModal from '../components/ManagerModal'
import './ManagersPage.css'

const ManagersPage = () => {
  const [managers, setManagers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [showModal, setShowModal] = useState(false)
  const [editingManager, setEditingManager] = useState(null)

  useEffect(() => {
    fetchManagers()
  }, [])

  const fetchManagers = async () => {
    try {
      setLoading(true)
      const response = await employerAPI.getManagers()
      setManagers(response.data)
    } catch (err) {
      setError('Failed to load managers')
      console.error('Error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleCreateManager = () => {
    setEditingManager(null)
    setShowModal(true)
  }

  const handleEditManager = (manager) => {
    setEditingManager(manager)
    setShowModal(true)
  }

  const handleDeleteManager = async (managerId) => {
    if (!window.confirm('Are you sure you want to delete this manager?')) return

    try {
      await employerAPI.deleteManager(managerId)
      setManagers(managers.filter(manager => manager._id !== managerId))
    } catch (err) {
      setError('Failed to delete manager')
      console.error('Error:', err)
    }
  }

  const handleModalClose = () => {
    setShowModal(false)
    setEditingManager(null)
  }

  const handleModalSuccess = () => {
    setShowModal(false)
    setEditingManager(null)
    fetchManagers()
  }

  if (loading) {
    return <div className="loading">Loading managers...</div>
  }

  return (
    <div className="managers-page">
      <div className="page-header">
        <div>
          <h1>👨‍💼 Managers</h1>
          <p>Manage your team of lead managers</p>
        </div>
        <button onClick={handleCreateManager} className="create-btn">
          + Add Manager
        </button>
      </div>

      {error && (
        <div className="error-message">
          {error}
        </div>
      )}

      {/* Managers Grid */}
      <div className="managers-grid">
        {managers.length > 0 ? (
          managers.map(manager => (
            <div key={manager._id} className="manager-card">
              <div className="manager-avatar">
                {manager.name.charAt(0).toUpperCase()}
              </div>
              
              <div className="manager-info">
                <h3>{manager.name}</h3>
                <p className="manager-email">{manager.email}</p>
                <p className="manager-role">Manager</p>
                <p className="manager-date">
                  Joined {new Date(manager.createdAt).toLocaleDateString()}
                </p>
              </div>

              <div className="manager-actions">
                <button 
                  onClick={() => handleEditManager(manager)}
                  className="edit-btn"
                >
                  Edit
                </button>
                <button 
                  onClick={() => handleDeleteManager(manager._id)}
                  className="delete-btn"
                >
                  Delete
                </button>
              </div>
            </div>
          ))
        ) : (
          <div className="empty-state">
            <div className="empty-icon">👨‍💼</div>
            <h3>No managers found</h3>
            <p>Add your first manager to start delegating leads</p>
            <button onClick={handleCreateManager} className="create-btn">
              + Add Manager
            </button>
          </div>
        )}
      </div>

      {/* Modal */}
      {showModal && (
        <ManagerModal
          manager={editingManager}
          onClose={handleModalClose}
          onSuccess={handleModalSuccess}
        />
      )}
    </div>
  )
}

export default ManagersPage
