.manager-dashboard {
  max-width: 1200px;
  margin: 0 auto;
}

/* Filter Tabs */
.filter-tabs {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-tab {
  background: white;
  border: 2px solid #e1e5e9;
  color: #666;
  padding: 0.75rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
}

.filter-tab:hover {
  border-color: #667eea;
  color: #667eea;
}

.filter-tab.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: #667eea;
  color: white;
}

.filter-tab .count {
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 700;
}

.filter-tab.active .count {
  background: rgba(255, 255, 255, 0.3);
}

/* Leads Section */
.leads-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e5e9;
  overflow: hidden;
}

.leads-list {
  display: flex;
  flex-direction: column;
}

.lead-card {
  padding: 1.5rem;
  border-bottom: 1px solid #e1e5e9;
  transition: background 0.2s ease;
}

.lead-card:last-child {
  border-bottom: none;
}

.lead-card:hover {
  background: #f8f9fa;
}

.lead-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 1rem;
}

.lead-info h3 {
  margin: 0 0 0.25rem 0;
  color: #333;
  font-size: 1.25rem;
  font-weight: 600;
}

.lead-email {
  color: #666;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
}

.lead-company {
  color: #667eea;
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.lead-status {
  flex-shrink: 0;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.lead-details {
  margin-bottom: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.lead-dates {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.lead-dates span {
  color: #666;
  font-size: 0.8rem;
}

.lead-notes {
  margin-top: 0.75rem;
}

.lead-notes strong {
  color: #333;
  font-size: 0.9rem;
}

.lead-notes p {
  margin: 0.25rem 0 0 0;
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

.lead-actions {
  display: flex;
  justify-content: flex-end;
}

.update-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.update-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: #666;
}

.empty-state h3 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1.5rem;
}

.empty-state p {
  margin: 0;
  font-size: 1.1rem;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .filter-tabs {
    justify-content: center;
  }

  .filter-tab {
    flex: 1;
    min-width: 120px;
    justify-content: center;
  }

  .lead-header {
    flex-direction: column;
    align-items: stretch;
  }

  .lead-status {
    align-self: flex-start;
  }

  .lead-dates {
    flex-direction: column;
    gap: 0.25rem;
  }

  .lead-actions {
    justify-content: stretch;
  }

  .update-btn {
    width: 100%;
  }

  .empty-state {
    padding: 2rem 1rem;
  }
}
