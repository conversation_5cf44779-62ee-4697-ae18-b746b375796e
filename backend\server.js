import express from 'express';
import cors from 'cors';
import { AuthRouter } from "./routes/auth.routes.js";
import { <PERSON><PERSON>oyerRouter } from './routes/employer.route.js';
import { ManagerRoutes } from './routes/manager.routes.js';
import { LeadsRouter } from './routes/leads.routes.js';
import dotenv from 'dotenv';
import mongoose from 'mongoose';


dotenv.config({ path: './config/.env' });
const PORT = process.env.PORT || 3000;
const app = express();

// CORS configuration
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));

app.use(express.json());

// Add routes before database connection
console.log('Setting up routes...');

// Add a simple test route first
app.get('/api/test', (req, res) => {
  console.log('Test route hit!');
  res.json({ message: 'API is working!' });
});

// Add health check route directly
app.get('/api/health', (req, res) => {
  console.log('Health route hit!');
  res.json({
    status: 'OK',
    message: 'Lead Management API is running',
    timestamp: new Date().toISOString()
  });
});

console.log('Routes set up successfully');

mongoose.connect(process.env.MONGO_URL)
.then(() => console.log('MongoDB connected'))
.catch((err) => console.error('MongoDB connection error:', err));

// Comment out all other routes temporarily to isolate the issue
// app.use('/api', LeadsRouter);
// app.use('/api/auth', AuthRouter);
// app.use('/api/employer', EmpoloyerRouter);
// app.use('/api/managers', ManagerRoutes);


app.listen(PORT, () => {
  console.log(`listening on port ${PORT}`)
});
